import { isNativePlatform } from './capacitor';

export interface AudioRecordingResult {
  success: boolean;
  audioBlob?: Blob;
  audioUrl?: string;
  duration?: number;
  error?: string;
}

export interface AudioRecordingOptions {
  maxDuration?: number; // in seconds
  sampleRate?: number;
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
}

class AudioRecordingManager {
  private isRecording = false;
  private mediaRecorder: MediaRecorder | null = null;
  private audioChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private startTime: number = 0;
  private maxDuration: number = 60; // default 60 seconds

  /**
   * Save debug audio data for development analysis
   */
  private async saveDebugAudio(base64Data: string | null, audioBlob: Blob, type: string): Promise<void> {
    try {
      console.log(`🔍 Attempting to save debug audio (${type})...`);
      
      // Try to use Capacitor Filesystem API for native platforms
      if (typeof window !== 'undefined' && (window as any).Capacitor?.isNativePlatform()) {
        try {
          const { Filesystem, Directory } = await import('@capacitor/filesystem');
          
          const timestamp = Date.now();
          const filename = `debug_audio_${type}_${timestamp}.${type.includes('aac') ? 'aac' : 'webm'}`;
          const base64Filename = `debug_audio_base64_${type}_${timestamp}.txt`;
          
          if (base64Data) {
            // Save the audio file
            await Filesystem.writeFile({
              path: filename,
              data: base64Data,
              directory: Directory.Documents,
            });
            
            // Save the base64 data for analysis
            await Filesystem.writeFile({
              path: base64Filename,
              data: base64Data,
              directory: Directory.Documents,
            });
            
            console.log(`📁 Debug audio saved to device Documents:`, { filename, base64Filename });
            console.log(`📄 File info:`, { 
              size: audioBlob.size, 
              type: audioBlob.type, 
              base64Length: base64Data.length 
            });
          }
        } catch (fsError) {
          console.warn('Capacitor Filesystem failed, trying fallback:', fsError);
          this.saveDebugAudioFallback(base64Data, audioBlob, type);
        }
      } else {
        // Web platform - use download links
        this.saveDebugAudioFallback(base64Data, audioBlob, type);
      }
    } catch (error) {
      console.error('Failed to save debug audio:', error);
    }
  }

  /**
   * Fallback method for saving debug audio using download links
   */
  private saveDebugAudioFallback(base64Data: string | null, audioBlob: Blob, type: string): void {
    try {
      const timestamp = Date.now();
      
      // Save the audio blob
      const audioUrl = URL.createObjectURL(audioBlob);
      const audioLink = document.createElement('a');
      audioLink.href = audioUrl;
      audioLink.download = `debug_audio_${type}_${timestamp}.${type.includes('aac') ? 'aac' : 'webm'}`;
      document.body.appendChild(audioLink);
      audioLink.click();
      document.body.removeChild(audioLink);
      
      // Save the base64 data if available
      if (base64Data) {
        const base64Blob = new Blob([base64Data], { type: 'text/plain' });
        const base64Url = URL.createObjectURL(base64Blob);
        const base64Link = document.createElement('a');
        base64Link.href = base64Url;
        base64Link.download = `debug_audio_base64_${type}_${timestamp}.txt`;
        document.body.appendChild(base64Link);
        base64Link.click();
        document.body.removeChild(base64Link);
      }
      
      console.log(`💾 Debug audio saved via download (${type}):`, { 
        size: audioBlob.size, 
        type: audioBlob.type,
        base64Length: base64Data?.length || 0
      });
    } catch (error) {
      console.error('Fallback debug save failed:', error);
    }
  }

  async startRecording(options: AudioRecordingOptions = {}): Promise<boolean> {
    if (this.isRecording) {
      console.warn('Recording already in progress');
      return false;
    }

    this.maxDuration = options.maxDuration || 60;

    if (isNativePlatform()) {
      return this.startNativeRecording();
    } else {
      return this.startWebRecording(options);
    }
  }

  async stopRecording(): Promise<AudioRecordingResult> {
    if (!this.isRecording) {
      return {
        success: false,
        error: 'No recording in progress'
      };
    }

    if (isNativePlatform()) {
      return this.stopNativeRecording();
    } else {
      return this.stopWebRecording();
    }
  }

  private async startNativeRecording(): Promise<boolean> {
    try {
      const { VoiceRecorder } = await import('capacitor-voice-recorder');
      
      // Check permission first
      const hasPermission = await VoiceRecorder.hasAudioRecordingPermission();
      if (!hasPermission.value) {
        const permission = await VoiceRecorder.requestAudioRecordingPermission();
        if (!permission.value) {
          console.warn('Audio recording permission denied');
          return false;
        }
      }

      // Start recording
      await VoiceRecorder.startRecording();
      this.isRecording = true;
      this.startTime = Date.now();

      // Set up auto-stop timer
      setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, this.maxDuration * 1000);

      console.log('Native recording started');
      return true;
    } catch (error) {
      console.error('Failed to start native recording:', error);
      return false;
    }
  }

  private async stopNativeRecording(): Promise<AudioRecordingResult> {
    try {
      const { VoiceRecorder } = await import('capacitor-voice-recorder');
      
      const result = await VoiceRecorder.stopRecording();
      this.isRecording = false;
      
      const duration = (Date.now() - this.startTime) / 1000;

      if (result.value && result.value.recordDataBase64) {
        // Convert base64 to blob
        const base64Data = result.value.recordDataBase64;
        if (!base64Data) {
          return {
            success: false,
            error: 'No base64 audio data received from native recorder'
          };
        }
        
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        
        const audioBlob = new Blob([bytes], { type: 'audio/aac' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Debug: Save raw audio immediately after creation (before any processing)
        console.log('🎤 Native AAC audio captured:', { 
          size: audioBlob.size, 
          type: audioBlob.type,
          duration: duration,
          firstBytes: result.value.recordDataBase64?.substring(0, 100) || 'no base64 data'
        });
        
        // Save audio to device storage for debugging (both blob and base64)
        // Enable debug saving in development environments
        const isDevelopment = typeof window !== 'undefined' && (
          window.location.hostname === 'localhost' || 
          window.location.hostname.includes('10.0') ||
          window.location.hostname === '127.0.0.1' ||
          window.location.protocol === 'http:' ||
          process.env.NODE_ENV === 'development'
        );
        
        console.log('🔍 Debug save check:', { 
          hostname: window?.location?.hostname || 'unknown',
          protocol: window?.location?.protocol || 'unknown',
          isDevelopment 
        });
        
        if (isDevelopment) {
          this.saveDebugAudio(result.value.recordDataBase64 || null, audioBlob, 'native_aac');
        }

        console.log('Native recording stopped successfully');
        return {
          success: true,
          audioBlob,
          audioUrl,
          duration
        };
      } else {
        return {
          success: false,
          error: 'No audio data received from native recorder'
        };
      }
    } catch (error) {
      this.isRecording = false;
      console.error('Failed to stop native recording:', error);
      return {
        success: false,
        error: `Failed to stop recording: ${error}`
      };
    }
  }

  private async startWebRecording(options: AudioRecordingOptions): Promise<boolean> {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: options.echoCancellation ?? true,
          noiseSuppression: options.noiseSuppression ?? true,
          autoGainControl: options.autoGainControl ?? true,
          sampleRate: options.sampleRate ?? 44100
        }
      });

      this.audioChunks = [];
      this.mediaRecorder = new MediaRecorder(this.stream);
      
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.start();
      this.isRecording = true;
      this.startTime = Date.now();

      // Set up auto-stop timer
      setTimeout(() => {
        if (this.isRecording) {
          this.stopRecording();
        }
      }, this.maxDuration * 1000);

      console.log('Web recording started');
      return true;
    } catch (error) {
      console.error('Failed to start web recording:', error);
      return false;
    }
  }

  private async stopWebRecording(): Promise<AudioRecordingResult> {
    return new Promise((resolve) => {
      if (!this.mediaRecorder) {
        resolve({
          success: false,
          error: 'No media recorder available'
        });
        return;
      }

      this.mediaRecorder.onstop = () => {
        const duration = (Date.now() - this.startTime) / 1000;
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        const audioUrl = URL.createObjectURL(audioBlob);

        // Debug: Save raw audio immediately after creation (before any processing)
        console.log('🎤 Web WebM audio captured:', { 
          size: audioBlob.size, 
          type: audioBlob.type,
          duration: duration 
        });
        
        // Save audio to device storage for debugging
        // Enable debug saving in development environments
        const isDevelopment = typeof window !== 'undefined' && (
          window.location.hostname === 'localhost' || 
          window.location.hostname.includes('10.0') ||
          window.location.hostname === '127.0.0.1' ||
          window.location.protocol === 'http:' ||
          process.env.NODE_ENV === 'development'
        );
        
        console.log('🔍 Web debug save check:', { 
          hostname: window?.location?.hostname || 'unknown',
          protocol: window?.location?.protocol || 'unknown',
          isDevelopment 
        });
        
        if (isDevelopment) {
          // Convert blob to base64 for consistent saving
          const reader = new FileReader();
          reader.onload = () => {
            const base64Data = (reader.result as string).split(',')[1];
            this.saveDebugAudio(base64Data, audioBlob, 'web_webm');
          };
          reader.readAsDataURL(audioBlob);
        }

        // Clean up
        if (this.stream) {
          this.stream.getTracks().forEach(track => track.stop());
          this.stream = null;
        }
        this.mediaRecorder = null;
        this.audioChunks = [];

        console.log('Web recording stopped successfully');
        resolve({
          success: true,
          audioBlob,
          audioUrl,
          duration
        });
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  cleanup(): void {
    if (this.isRecording) {
      this.stopRecording();
    }
    
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    this.mediaRecorder = null;
    this.audioChunks = [];
  }
}

// Export a singleton instance
export const audioRecorder = new AudioRecordingManager();

// Export utility functions
export const startAudioRecording = (options?: AudioRecordingOptions) => 
  audioRecorder.startRecording(options);

export const stopAudioRecording = () => 
  audioRecorder.stopRecording();

export const isRecording = () => 
  audioRecorder.isCurrentlyRecording();

export const cleanupAudioRecording = () => 
  audioRecorder.cleanup();
