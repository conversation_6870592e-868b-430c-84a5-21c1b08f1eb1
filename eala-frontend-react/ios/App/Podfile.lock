PODS:
  - Capacitor (7.4.2):
    - Capacitor<PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorCordova (7.4.2)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorFilesystem (7.1.2):
    - Capacitor
    - IONFilesystemLib (~> 1.0)
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorShare (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapacitorVoiceRecorder (7.0.6):
    - Capacitor
  - IONFilesystemLib (1.0.0)

DEPENDENCIES:
  - "Capacitor (from `../../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../../node_modules/@capacitor/filesystem`)"
  - "CapacitorHaptics (from `../../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../../node_modules/@capacitor/keyboard`)"
  - "CapacitorShare (from `../../../node_modules/@capacitor/share`)"
  - "CapacitorStatusBar (from `../../../node_modules/@capacitor/status-bar`)"
  - CapacitorVoiceRecorder (from `../../../node_modules/capacitor-voice-recorder`)

SPEC REPOS:
  trunk:
    - IONFilesystemLib

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../../node_modules/@capacitor/filesystem"
  CapacitorHaptics:
    :path: "../../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../../node_modules/@capacitor/keyboard"
  CapacitorShare:
    :path: "../../../node_modules/@capacitor/share"
  CapacitorStatusBar:
    :path: "../../../node_modules/@capacitor/status-bar"
  CapacitorVoiceRecorder:
    :path: "../../../node_modules/capacitor-voice-recorder"

SPEC CHECKSUMS:
  Capacitor: 9d9e481b79ffaeacaf7a85d6a11adec32bd33b59
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorDevice: c6f6d587dd310527f8a48bf09c4e7b4a4cf14329
  CapacitorFilesystem: 337eeaf16a9fa5a44d2329df411523fa3a9a6ee9
  CapacitorHaptics: 1f1e17041f435d8ead9ff2a34edd592c6aa6a8d6
  CapacitorKeyboard: 09fd91dcde4f8a37313e7f11bde553ad1ed52036
  CapacitorShare: e573823f511f260f598d0423c33b1e3d7bbe5fd1
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  CapacitorVoiceRecorder: 9158861be982f32d411769723a4d813d6ce46135
  IONFilesystemLib: ceacae793975039530458eabab0c495c70515a0d

PODFILE CHECKSUM: dbb796d60efb23647db9e45a153ab447b4f34612

COCOAPODS: 1.16.2
